import { FreebieService } from '../services/freebie.service';
import { FreebieProduct } from '../models/freebie.model';
import { CartItem } from '../models/product.model';

export interface BillingTab {
  items: CartItem[];
  freebiesProducts: FreebieProduct[];
  showFreebies: boolean;
  currentCartAmount: number;
  addedFreebies: Set<string>;
}

export class FreebieManager {
  constructor(private freebieService: FreebieService) {}

  async initializeFreebies(tab: BillingTab, cartAmount: number): Promise<void> {
    tab.currentCartAmount = cartAmount;
    await this.updateFreebies(tab);
  }

  async checkCartAmountAndUpdate(tab: BillingTab, newCartAmount: number): Promise<boolean> {
    if (newCartAmount === tab.currentCartAmount) return false;
    
    tab.currentCartAmount = newCartAmount;
    if (newCartAmount === 0) {
      this.clearFreebiesOnCartClear(tab);
    } else {
      await this.updateFreebies(tab);
    }
    return true;
  }

  clearFreebiesOnCartClear(tab: BillingTab): void {
    tab.items = this.freebieService.removeAllFreebies(tab.items);
    tab.addedFreebies.clear();
    tab.freebiesProducts = [];
    tab.showFreebies = false;
  }

  addFreebie(tab: BillingTab, freebie: FreebieProduct, validate: boolean = true): boolean {
    if (validate && !this.freebieService.canAddFreebie(tab.currentCartAmount, freebie, tab.items)) {
      return false;
    }
    tab.items.push(this.freebieService.createFreebieCartItem(freebie));
    tab.addedFreebies.add(freebie.id);
    return true;
  }

  handleItemRemoval(tab: BillingTab, removedItem: any): void {
    if (removedItem?.is_freebie) tab.addedFreebies.delete(removedItem.freebie_id);
  }

  getFreebieButtonState(tab: BillingTab, freebie: FreebieProduct) {
    const isInCart = this.freebieService.isFreebieInCart(tab.items, freebie.id);
    return {
      hidden: isInCart,
      disabled: !(tab.currentCartAmount >= freebie.amount) || isInCart
    };
  }

  private async updateFreebies(tab: BillingTab): Promise<void> {
    const freebies = await this.freebieService.getAvailableFreebies(tab.currentCartAmount);
    tab.freebiesProducts = freebies;
    tab.showFreebies = freebies.length > 0;
  }
}
