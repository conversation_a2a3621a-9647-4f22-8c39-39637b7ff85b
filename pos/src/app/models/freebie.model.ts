import { CartItem } from './product.model';
// Freebie product from the freebies_products collection
export interface FreebieProduct {
  id: string;
  name: string;
  product_id: string;
  sku: string;
  variant_id: string;
  amount: number;
  available_qty: number;
  start_date: number;
  end_date: number;
  store_id: string;
  // Product details that will be populated
  thumbnail_image?: string;
  selling_price?: number;
  variant_name?: string;
  child_sku?: string;
}
// Extended CartItem with freebie-related properties 
export interface FreebieCartItem extends CartItem {
  is_freebie: boolean;
  freebie_id?: string;
  freebie_amount?: number;
  available_qty?: number;
  freebie_name?: string;
}
